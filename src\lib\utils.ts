import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import { CONVERSION_FACTORS, RTL_LANGUAGES, type Locale } from "./constants";

/**
 * Utility function to merge Tailwind CSS classes
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Check if a locale uses RTL (Right-to-Left) text direction
 */
export function isRTL(locale: string): boolean {
  return RTL_LANGUAGES.includes(locale as Locale);
}

/**
 * Format a number with locale-specific formatting
 */
export function formatNumber(
  value: number,
  locale: string,
  options?: Intl.NumberFormatOptions
): string {
  return new Intl.NumberFormat(locale, options).format(value);
}

/**
 * Format currency with locale-specific formatting
 */
export function formatCurrency(
  value: number,
  locale: string,
  currency: string = 'USD'
): string {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
  }).format(value);
}

/**
 * Format percentage with locale-specific formatting
 */
export function formatPercentage(
  value: number,
  locale: string,
  decimals: number = 2
): string {
  return new Intl.NumberFormat(locale, {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value / 100);
}

/**
 * Calculate factorial of a number
 */
export function factorial(n: number): number {
  if (n < 0) return NaN;
  if (n === 0 || n === 1) return 1;
  let result = 1;
  for (let i = 2; i <= n; i++) {
    result *= i;
  }
  return result;
}

/**
 * Convert degrees to radians
 */
export function degreesToRadians(degrees: number): number {
  return degrees * (Math.PI / 180);
}

/**
 * Convert radians to degrees
 */
export function radiansToDegrees(radians: number): number {
  return radians * (180 / Math.PI);
}

/**
 * Calculate compound interest
 */
export function calculateCompoundInterest(
  principal: number,
  rate: number,
  time: number,
  compoundFrequency: number = 12
): number {
  return principal * Math.pow(1 + rate / compoundFrequency, compoundFrequency * time);
}

/**
 * Calculate monthly loan payment
 */
export function calculateLoanPayment(
  principal: number,
  annualRate: number,
  years: number
): number {
  const monthlyRate = annualRate / 12;
  const numberOfPayments = years * 12;
  
  if (monthlyRate === 0) {
    return principal / numberOfPayments;
  }
  
  return principal * (monthlyRate * Math.pow(1 + monthlyRate, numberOfPayments)) / 
         (Math.pow(1 + monthlyRate, numberOfPayments) - 1);
}

/**
 * Convert temperature between different units
 */
export function convertTemperature(
  value: number,
  fromUnit: string,
  toUnit: string
): number {
  if (fromUnit === toUnit) return value;
  
  // Convert to Celsius first
  let celsius: number;
  switch (fromUnit) {
    case 'fahrenheit':
      celsius = (value - 32) * 5/9;
      break;
    case 'kelvin':
      celsius = value - 273.15;
      break;
    default:
      celsius = value;
  }
  
  // Convert from Celsius to target unit
  switch (toUnit) {
    case 'fahrenheit':
      return (celsius * 9/5) + 32;
    case 'kelvin':
      return celsius + 273.15;
    default:
      return celsius;
  }
}

/**
 * Convert units using conversion factors
 */
export function convertUnit(
  value: number,
  fromUnit: string,
  toUnit: string,
  category: keyof typeof CONVERSION_FACTORS
): number {
  if (fromUnit === toUnit) return value;
  
  const factors = CONVERSION_FACTORS[category] as Record<string, number>;
  
  if (!factors[fromUnit] || !factors[toUnit]) {
    throw new Error(`Invalid units for category ${category}`);
  }
  
  // Convert to base unit, then to target unit
  const baseValue = value * factors[fromUnit];
  return baseValue / factors[toUnit];
}

/**
 * Calculate date difference in various units
 */
export function calculateDateDifference(
  startDate: Date,
  endDate: Date
): {
  years: number;
  months: number;
  days: number;
  totalDays: number;
} {
  const timeDiff = endDate.getTime() - startDate.getTime();
  const totalDays = Math.ceil(timeDiff / (1000 * 3600 * 24));

  let years = endDate.getFullYear() - startDate.getFullYear();
  let months = endDate.getMonth() - startDate.getMonth();
  let days = endDate.getDate() - startDate.getDate();

  if (days < 0) {
    months--;
    const lastMonth = new Date(endDate.getFullYear(), endDate.getMonth(), 0);
    days += lastMonth.getDate();
  }

  if (months < 0) {
    years--;
    months += 12;
  }

  return {
    years: Math.abs(years),
    months: Math.abs(months),
    days: Math.abs(days),
    totalDays: Math.abs(totalDays)
  };
}

/**
 * Add or subtract time from a date
 */
export function addSubtractDate(
  baseDate: Date,
  amount: number,
  unit: string,
  operation: 'add' | 'subtract'
): Date {
  const result = new Date(baseDate);
  const multiplier = operation === 'add' ? 1 : -1;
  const adjustedAmount = amount * multiplier;

  switch (unit) {
    case 'days':
      result.setDate(result.getDate() + adjustedAmount);
      break;
    case 'weeks':
      result.setDate(result.getDate() + (adjustedAmount * 7));
      break;
    case 'months':
      result.setMonth(result.getMonth() + adjustedAmount);
      break;
    case 'years':
      result.setFullYear(result.getFullYear() + adjustedAmount);
      break;
  }

  return result;
}

/**
 * Validate and parse number input
 */
export function parseNumberInput(input: string): number | null {
  const trimmed = input.trim();
  if (trimmed === '') return null;
  
  const parsed = parseFloat(trimmed);
  return isNaN(parsed) ? null : parsed;
}

/**
 * Round number to specified decimal places
 */
export function roundToDecimals(value: number, decimals: number): number {
  return Math.round(value * Math.pow(10, decimals)) / Math.pow(10, decimals);
}

/**
 * Check if a number is within a valid range
 */
export function isInRange(value: number, min: number, max: number): boolean {
  return value >= min && value <= max;
}
