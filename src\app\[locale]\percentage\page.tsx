'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Calculator } from 'lucide-react';

export default function PercentageCalculator() {
  const t = useTranslations('percentage');
  const tCommon = useTranslations('common');
  
  const [value1, setValue1] = useState('');
  const [value2, setValue2] = useState('');
  const [percent, setPercent] = useState('');
  const [results, setResults] = useState({
    percentOf: '',
    whatPercent: '',
    percentChange: ''
  });

  const calculatePercentOf = () => {
    const num = parseFloat(value1);
    const pct = parseFloat(percent);
    if (!isNaN(num) && !isNaN(pct)) {
      const result = (num * pct) / 100;
      setResults(prev => ({ ...prev, percentOf: result.toString() }));
    }
  };

  const calculateWhatPercent = () => {
    const num = parseFloat(value1);
    const total = parseFloat(value2);
    if (!isNaN(num) && !isNaN(total) && total !== 0) {
      const result = (num / total) * 100;
      setResults(prev => ({ ...prev, whatPercent: result.toFixed(2) + '%' }));
    }
  };

  const calculatePercentChange = () => {
    const oldVal = parseFloat(value1);
    const newVal = parseFloat(value2);
    if (!isNaN(oldVal) && !isNaN(newVal) && oldVal !== 0) {
      const result = ((newVal - oldVal) / oldVal) * 100;
      setResults(prev => ({ ...prev, percentChange: result.toFixed(2) + '%' }));
    }
  };

  const clearAll = () => {
    setValue1('');
    setValue2('');
    setPercent('');
    setResults({
      percentOf: '',
      whatPercent: '',
      percentChange: ''
    });
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8">
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-400 mb-4">
            <Calculator className="w-8 h-8" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            {t('title')}
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            {t('description')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* What is X% of Y? */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              What is {percent || 'X'}% of {value1 || 'Y'}?
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Percentage
                </label>
                <input
                  type="number"
                  value={percent}
                  onChange={(e) => setPercent(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                  placeholder="Enter percentage"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Number
                </label>
                <input
                  type="number"
                  value={value1}
                  onChange={(e) => setValue1(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                  placeholder="Enter number"
                />
              </div>
              <button
                onClick={calculatePercentOf}
                className="w-full bg-indigo-500 hover:bg-indigo-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200"
              >
                {tCommon('calculate')}
              </button>
              {results.percentOf && (
                <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border-l-4 border-indigo-500">
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {tCommon('result')}: {results.percentOf}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* X is what percent of Y? */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              {value1 || 'X'} is what percent of {value2 || 'Y'}?
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Number
                </label>
                <input
                  type="number"
                  value={value1}
                  onChange={(e) => setValue1(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                  placeholder="Enter number"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Total
                </label>
                <input
                  type="number"
                  value={value2}
                  onChange={(e) => setValue2(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                  placeholder="Enter total"
                />
              </div>
              <button
                onClick={calculateWhatPercent}
                className="w-full bg-indigo-500 hover:bg-indigo-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200"
              >
                {tCommon('calculate')}
              </button>
              {results.whatPercent && (
                <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border-l-4 border-indigo-500">
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {tCommon('result')}: {results.whatPercent}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Percentage Change */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Percentage change from {value1 || 'X'} to {value2 || 'Y'}
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Old Value
                </label>
                <input
                  type="number"
                  value={value1}
                  onChange={(e) => setValue1(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                  placeholder="Enter old value"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  New Value
                </label>
                <input
                  type="number"
                  value={value2}
                  onChange={(e) => setValue2(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                  placeholder="Enter new value"
                />
              </div>
              <button
                onClick={calculatePercentChange}
                className="w-full bg-indigo-500 hover:bg-indigo-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200"
              >
                {tCommon('calculate')}
              </button>
              {results.percentChange && (
                <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border-l-4 border-indigo-500">
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {tCommon('result')}: {results.percentChange}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="mt-8 text-center">
          <button
            onClick={clearAll}
            className="bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-6 rounded-lg transition-colors duration-200"
          >
            {tCommon('clear')} All
          </button>
        </div>
      </div>
    </div>
  );
}
