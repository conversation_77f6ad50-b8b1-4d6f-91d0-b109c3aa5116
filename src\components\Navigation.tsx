'use client';

import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Calculator, Home, TrendingUp, RotateCcw, Calendar, Percent, Atom } from 'lucide-react';
import { clsx } from 'clsx';

export default function Navigation() {
  const t = useTranslations('navigation');
  const pathname = usePathname();

  const navigation = [
    { name: t('home'), href: '/', icon: Home },
    { name: t('basic'), href: '/basic', icon: Calculator },
    { name: t('scientific'), href: '/scientific', icon: Atom },
    { name: t('financial'), href: '/financial', icon: TrendingUp },
    { name: t('unit'), href: '/unit', icon: RotateCcw },
    { name: t('date'), href: '/date', icon: Calendar },
    { name: t('percentage'), href: '/percentage', icon: Percent },
  ];

  return (
    <nav className="flex space-x-8">
      {navigation.map((item) => {
        const Icon = item.icon;
        const isActive = pathname === item.href || (item.href !== '/' && pathname.startsWith(item.href));
        
        return (
          <Link
            key={item.name}
            href={item.href}
            className={clsx(
              'inline-flex items-center px-1 pt-1 text-sm font-medium border-b-2 transition-colors duration-200',
              isActive
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
            )}
          >
            <Icon className="w-4 h-4 mr-2" />
            {item.name}
          </Link>
        );
      })}
    </nav>
  );
}
