// Supported locales
export const LOCALES = ['en', 'fr', 'es', 'ar'] as const;
export const DEFAULT_LOCALE = 'en' as const;

export type Locale = typeof LOCALES[number];

// Language names for display
export const LANGUAGE_NAMES: Record<Locale, string> = {
  en: 'English',
  fr: 'Français',
  es: 'Español',
  ar: 'العربية'
};

// RTL languages
export const RTL_LANGUAGES: Locale[] = ['ar'];

// Calculator types
export const CALCULATOR_TYPES = [
  'basic',
  'scientific',
  'financial',
  'unit',
  'date',
  'percentage'
] as const;

export type CalculatorType = typeof CALCULATOR_TYPES[number];

// Unit conversion categories
export const UNIT_CATEGORIES = [
  'length',
  'weight',
  'temperature',
  'area',
  'volume',
  'speed',
  'time'
] as const;

export type UnitCategory = typeof UNIT_CATEGORIES[number];

// Financial calculator types
export const FINANCIAL_TYPES = [
  'loan',
  'mortgage',
  'investment'
] as const;

export type FinancialType = typeof FINANCIAL_TYPES[number];

// Date calculator operations
export const DATE_OPERATIONS = ['add', 'subtract'] as const;
export const DATE_UNITS = ['days', 'weeks', 'months', 'years'] as const;

export type DateOperation = typeof DATE_OPERATIONS[number];
export type DateUnit = typeof DATE_UNITS[number];

// Mathematical constants
export const MATH_CONSTANTS = {
  PI: Math.PI,
  E: Math.E,
  GOLDEN_RATIO: (1 + Math.sqrt(5)) / 2,
  EULER_GAMMA: 0.5772156649015329
} as const;

// Conversion factors (to base unit)
export const CONVERSION_FACTORS = {
  length: {
    meter: 1,
    kilometer: 1000,
    centimeter: 0.01,
    millimeter: 0.001,
    inch: 0.0254,
    foot: 0.3048,
    yard: 0.9144,
    mile: 1609.34
  },
  weight: {
    kilogram: 1,
    gram: 0.001,
    pound: 0.453592,
    ounce: 0.0283495,
    ton: 1000,
    stone: 6.35029
  },
  area: {
    square_meter: 1,
    square_kilometer: 1000000,
    square_centimeter: 0.0001,
    square_inch: 0.00064516,
    square_foot: 0.092903,
    acre: 4046.86,
    hectare: 10000
  },
  volume: {
    liter: 1,
    milliliter: 0.001,
    gallon: 3.78541,
    quart: 0.946353,
    pint: 0.473176,
    cup: 0.236588,
    cubic_meter: 1000,
    cubic_centimeter: 0.001
  }
} as const;

// Unit labels for display
export const UNIT_LABELS = {
  length: {
    meter: 'm',
    kilometer: 'km',
    centimeter: 'cm',
    millimeter: 'mm',
    inch: 'in',
    foot: 'ft',
    yard: 'yd',
    mile: 'mi'
  },
  weight: {
    kilogram: 'kg',
    gram: 'g',
    pound: 'lb',
    ounce: 'oz',
    ton: 't',
    stone: 'st'
  },
  temperature: {
    celsius: '°C',
    fahrenheit: '°F',
    kelvin: 'K'
  },
  area: {
    square_meter: 'm²',
    square_kilometer: 'km²',
    square_centimeter: 'cm²',
    square_inch: 'in²',
    square_foot: 'ft²',
    acre: 'acre',
    hectare: 'ha'
  },
  volume: {
    liter: 'L',
    milliliter: 'mL',
    gallon: 'gal',
    quart: 'qt',
    pint: 'pt',
    cup: 'cup',
    cubic_meter: 'm³',
    cubic_centimeter: 'cm³'
  }
} as const;
