# Calculator Suite

A comprehensive Next.js calculator application with internationalization support, featuring multiple calculator types similar to calculator.net.

## Features

### 🧮 Calculator Types
- **Basic Calculator** - Standard arithmetic operations
- **Scientific Calculator** - Advanced mathematical functions (sin, cos, tan, log, sqrt, etc.)
- **Financial Calculator** - Loan and investment calculations
- **Unit Converter** - Convert between different units (length, weight, temperature, area, volume)
- **Date Calculator** - Date differences and add/subtract operations
- **Percentage Calculator** - Various percentage calculations

### 🌍 Internationalization
- **4 Languages**: English, French, Spanish, and Arabic
- **RTL Support** for Arabic
- **Translated URLs**:
  - English: `/` (root)
  - French: `/fr/`
  - Spanish: `/es/`
  - Arabic: `/ar/`
- **Locale-specific formatting** for numbers and dates

### 🎨 Design Features
- **Responsive Design** - Works on desktop, tablet, and mobile
- **Modern UI** with Tailwind CSS
- **Dark/Light Theme** support
- **Smooth Animations** and transitions
- **Professional Layout** with clean typography

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Internationalization**: next-intl
- **Icons**: Lucide React
- **Deployment**: Vercel-ready

## Getting Started

### Prerequisites
- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd calculator
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

## Project Structure

```
src/
├── app/
│   ├── [locale]/           # Internationalized routes
│   │   ├── basic/          # Basic calculator
│   │   ├── scientific/     # Scientific calculator
│   │   ├── financial/      # Financial calculator
│   │   ├── unit/           # Unit converter
│   │   ├── date/           # Date calculator
│   │   ├── percentage/     # Percentage calculator
│   │   ├── layout.tsx      # Locale-specific layout
│   │   └── page.tsx        # Home page
│   ├── globals.css         # Global styles
│   └── layout.tsx          # Root layout
├── components/
│   ├── Navigation.tsx      # Main navigation
│   └── LanguageSwitcher.tsx # Language selector
├── i18n/
│   └── request.ts          # i18n configuration
└── middleware.ts           # Route middleware
messages/                   # Translation files
├── en.json                 # English translations
├── fr.json                 # French translations
├── es.json                 # Spanish translations
└── ar.json                 # Arabic translations
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## Internationalization

The app supports 4 languages with complete translations:

### Adding New Languages
1. Add the locale to `src/i18n/request.ts`
2. Create a new translation file in `messages/[locale].json`
3. Update the middleware configuration
4. Add the language to the language switcher

### URL Structure
- English (default): `https://example.com/basic`
- French: `https://example.com/fr/basic`
- Spanish: `https://example.com/es/basic`
- Arabic: `https://example.com/ar/basic`

## Calculator Features

### Basic Calculator
- Standard arithmetic operations (+, -, ×, ÷)
- Decimal support
- Clear and backspace functions
- Keyboard support

### Scientific Calculator
- Trigonometric functions (sin, cos, tan)
- Logarithmic functions (log, ln)
- Power and square root operations
- Constants (π, e)
- Factorial calculations

### Financial Calculator
- **Loan Calculator**: Monthly payments, total interest
- **Investment Calculator**: Future value with compound interest
- **Mortgage Calculator**: Home loan calculations

### Unit Converter
- **Length**: meters, kilometers, inches, feet, miles
- **Weight**: kilograms, grams, pounds, ounces
- **Temperature**: Celsius, Fahrenheit, Kelvin
- **Area**: square meters, acres, hectares
- **Volume**: liters, gallons, cubic meters

### Date Calculator
- **Date Difference**: Calculate years, months, days between dates
- **Add/Subtract**: Add or subtract time from a base date
- **Multiple Units**: days, weeks, months, years

### Percentage Calculator
- What is X% of Y?
- X is what percent of Y?
- Percentage change calculations

## Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy automatically

### Other Platforms
The app can be deployed to any platform that supports Next.js:
- Netlify
- AWS Amplify
- Railway
- DigitalOcean App Platform

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Acknowledgments

- Built with [Next.js](https://nextjs.org/)
- Styled with [Tailwind CSS](https://tailwindcss.com/)
- Icons by [Lucide](https://lucide.dev/)
- Internationalization by [next-intl](https://next-intl-docs.vercel.app/)
