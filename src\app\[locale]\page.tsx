import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { Calculator, TrendingUp, RotateCcw, Calendar, Percent, Atom, Smartphone, Monitor, Globe } from 'lucide-react';

export default function Home() {
  const t = useTranslations('home');
  const tNav = useTranslations('navigation');

  const calculators = [
    {
      name: tNav('basic'),
      href: '/basic',
      icon: Calculator,
      description: 'Basic arithmetic operations',
      color: 'bg-blue-500'
    },
    {
      name: tNav('scientific'),
      href: '/scientific',
      icon: Atom,
      description: 'Advanced mathematical functions',
      color: 'bg-purple-500'
    },
    {
      name: tNav('financial'),
      href: '/financial',
      icon: TrendingUp,
      description: 'Loans, mortgages, and investments',
      color: 'bg-green-500'
    },
    {
      name: tNav('unit'),
      href: '/unit',
      icon: RotateCcw,
      description: 'Convert between units',
      color: 'bg-orange-500'
    },
    {
      name: tNav('date'),
      href: '/date',
      icon: Calendar,
      description: 'Date calculations and differences',
      color: 'bg-red-500'
    },
    {
      name: tNav('percentage'),
      href: '/percentage',
      icon: Percent,
      description: 'Percentage calculations',
      color: 'bg-indigo-500'
    }
  ];

  const features = [
    {
      icon: Smartphone,
      title: t('features.responsive'),
      description: t('features.responsive_desc')
    },
    {
      icon: Globe,
      title: t('features.multilingual'),
      description: t('features.multilingual_desc')
    },
    {
      icon: Monitor,
      title: t('features.comprehensive'),
      description: t('features.comprehensive_desc')
    }
  ];

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      {/* Hero Section */}
      <div className="text-center mb-16">
        <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
          {t('title')}
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
          {t('subtitle')}
        </p>
        <p className="text-lg text-gray-500 dark:text-gray-400 max-w-4xl mx-auto">
          {t('description')}
        </p>
      </div>

      {/* Calculator Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
        {calculators.map((calc) => {
          const Icon = calc.icon;
          return (
            <Link
              key={calc.name}
              href={calc.href}
              className="group relative bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-200 dark:border-gray-700"
            >
              <div className="p-8">
                <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg ${calc.color} text-white mb-4`}>
                  <Icon className="w-6 h-6" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  {calc.name}
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {calc.description}
                </p>
              </div>
            </Link>
          );
        })}
      </div>

      {/* Features Section */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8 md:p-12">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-white text-center mb-12">
          {t('features.title')}
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <div key={index} className="text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 mb-4">
                  <Icon className="w-8 h-8" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {feature.description}
                </p>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
