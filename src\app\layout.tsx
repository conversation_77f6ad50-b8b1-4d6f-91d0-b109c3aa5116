import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "Calculator Suite - Professional Calculators",
  description: "Comprehensive calculator suite with basic, scientific, financial, unit conversion, date, and percentage calculators. Available in English, French, Spanish, and Arabic.",
  keywords: ["calculator", "scientific calculator", "financial calculator", "unit converter", "date calculator", "percentage calculator"],
  authors: [{ name: "Calculator Suite" }],
  creator: "Calculator Suite",
  publisher: "Calculator Suite",
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://calculator-suite.vercel.app",
    title: "Calculator Suite - Professional Calculators",
    description: "Comprehensive calculator suite with multiple calculator types and internationalization support.",
    siteName: "Calculator Suite",
  },
  twitter: {
    card: "summary_large_image",
    title: "Calculator Suite - Professional Calculators",
    description: "Comprehensive calculator suite with multiple calculator types and internationalization support.",
  },
};

export default function RootLayout({
  children,
}: <PERSON>only<{
  children: React.ReactNode;
}>) {
  return children;
}
