'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Calendar, Plus, Minus } from 'lucide-react';

export default function DateCalculator() {
  const t = useTranslations('date');
  const tCommon = useTranslations('common');
  
  const [activeTab, setActiveTab] = useState('difference');
  
  // Date Difference State
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [dateDifference, setDateDifference] = useState({
    years: 0,
    months: 0,
    days: 0,
    totalDays: 0
  });

  // Add/Subtract Date State
  const [baseDate, setBaseDate] = useState('');
  const [operation, setOperation] = useState('add');
  const [amount, setAmount] = useState('');
  const [unit, setUnit] = useState('days');
  const [resultDate, setResultDate] = useState('');

  const calculateDateDifference = () => {
    if (!startDate || !endDate) return;

    const start = new Date(startDate);
    const end = new Date(endDate);
    
    // Calculate total days
    const timeDiff = end.getTime() - start.getTime();
    const totalDays = Math.ceil(timeDiff / (1000 * 3600 * 24));

    // Calculate years, months, and days
    let years = end.getFullYear() - start.getFullYear();
    let months = end.getMonth() - start.getMonth();
    let days = end.getDate() - start.getDate();

    if (days < 0) {
      months--;
      const lastMonth = new Date(end.getFullYear(), end.getMonth(), 0);
      days += lastMonth.getDate();
    }

    if (months < 0) {
      years--;
      months += 12;
    }

    setDateDifference({
      years: Math.abs(years),
      months: Math.abs(months),
      days: Math.abs(days),
      totalDays: Math.abs(totalDays)
    });
  };

  const calculateAddSubtract = () => {
    if (!baseDate || !amount) return;

    const base = new Date(baseDate);
    const amountNum = parseInt(amount);
    const multiplier = operation === 'add' ? 1 : -1;

    let result = new Date(base);

    switch (unit) {
      case 'days':
        result.setDate(result.getDate() + (amountNum * multiplier));
        break;
      case 'weeks':
        result.setDate(result.getDate() + (amountNum * 7 * multiplier));
        break;
      case 'months':
        result.setMonth(result.getMonth() + (amountNum * multiplier));
        break;
      case 'years':
        result.setFullYear(result.getFullYear() + (amountNum * multiplier));
        break;
    }

    setResultDate(result.toISOString().split('T')[0]);
  };

  const clearDifference = () => {
    setStartDate('');
    setEndDate('');
    setDateDifference({ years: 0, months: 0, days: 0, totalDays: 0 });
  };

  const clearAddSubtract = () => {
    setBaseDate('');
    setAmount('');
    setResultDate('');
  };

  const tabs = [
    { id: 'difference', name: t('difference.title'), icon: Calendar },
    { id: 'add_subtract', name: t('add_subtract.title'), icon: Plus },
  ];

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8">
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 mb-4">
            <Calendar className="w-8 h-8" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            {t('title')}
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            {t('description')}
          </p>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1 mb-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex-1 flex items-center justify-center px-4 py-2 rounded-md font-medium transition-colors duration-200 ${
                  activeTab === tab.id
                    ? 'bg-white dark:bg-gray-800 text-red-600 dark:text-red-400 shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                <Icon className="w-4 h-4 mr-2" />
                {tab.name}
              </button>
            );
          })}
        </div>

        {/* Date Difference Calculator */}
        {activeTab === 'difference' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('difference.start_date')}
                </label>
                <input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('difference.end_date')}
                </label>
                <input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            <div className="flex space-x-4">
              <button
                onClick={calculateDateDifference}
                className="bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-6 rounded-lg transition-colors duration-200"
              >
                {tCommon('calculate')}
              </button>
              <button
                onClick={clearDifference}
                className="bg-gray-500 hover:bg-gray-600 text-white font-semibold py-2 px-6 rounded-lg transition-colors duration-200"
              >
                {tCommon('clear')}
              </button>
            </div>

            {/* Date Difference Results */}
            {(dateDifference.years > 0 || dateDifference.months > 0 || dateDifference.days > 0) && (
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="bg-red-50 dark:bg-red-900 rounded-lg p-4 text-center">
                  <h4 className="font-semibold text-red-800 dark:text-red-200 mb-2">
                    {t('difference.years')}
                  </h4>
                  <p className="text-2xl font-bold text-red-600 dark:text-red-400">
                    {dateDifference.years}
                  </p>
                </div>
                <div className="bg-blue-50 dark:bg-blue-900 rounded-lg p-4 text-center">
                  <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">
                    {t('difference.months')}
                  </h4>
                  <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {dateDifference.months}
                  </p>
                </div>
                <div className="bg-green-50 dark:bg-green-900 rounded-lg p-4 text-center">
                  <h4 className="font-semibold text-green-800 dark:text-green-200 mb-2">
                    {t('difference.days')}
                  </h4>
                  <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                    {dateDifference.days}
                  </p>
                </div>
                <div className="bg-purple-50 dark:bg-purple-900 rounded-lg p-4 text-center">
                  <h4 className="font-semibold text-purple-800 dark:text-purple-200 mb-2">
                    Total Days
                  </h4>
                  <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                    {dateDifference.totalDays}
                  </p>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Add/Subtract Date Calculator */}
        {activeTab === 'add_subtract' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('add_subtract.base_date')}
                </label>
                <input
                  type="date"
                  value={baseDate}
                  onChange={(e) => setBaseDate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('add_subtract.operation')}
                </label>
                <select
                  value={operation}
                  onChange={(e) => setOperation(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                >
                  <option value="add">{t('add_subtract.add')}</option>
                  <option value="subtract">{t('add_subtract.subtract')}</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('add_subtract.amount')}
                </label>
                <input
                  type="number"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="Enter amount"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('add_subtract.unit')}
                </label>
                <select
                  value={unit}
                  onChange={(e) => setUnit(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                >
                  <option value="days">Days</option>
                  <option value="weeks">Weeks</option>
                  <option value="months">Months</option>
                  <option value="years">Years</option>
                </select>
              </div>
            </div>

            <div className="flex space-x-4">
              <button
                onClick={calculateAddSubtract}
                className="bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-6 rounded-lg transition-colors duration-200"
              >
                {tCommon('calculate')}
              </button>
              <button
                onClick={clearAddSubtract}
                className="bg-gray-500 hover:bg-gray-600 text-white font-semibold py-2 px-6 rounded-lg transition-colors duration-200"
              >
                {tCommon('clear')}
              </button>
            </div>

            {/* Add/Subtract Results */}
            {resultDate && (
              <div className="bg-red-50 dark:bg-red-900 rounded-lg p-6 text-center">
                <h4 className="font-semibold text-red-800 dark:text-red-200 mb-2">
                  {tCommon('result')}
                </h4>
                <p className="text-3xl font-bold text-red-600 dark:text-red-400">
                  {new Date(resultDate).toLocaleDateString()}
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
