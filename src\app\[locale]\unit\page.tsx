'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { RotateCcw, ArrowRight } from 'lucide-react';

export default function UnitConverter() {
  const t = useTranslations('unit');
  const tCommon = useTranslations('common');
  
  const [activeCategory, setActiveCategory] = useState('length');
  const [fromUnit, setFromUnit] = useState('');
  const [toUnit, setToUnit] = useState('');
  const [inputValue, setInputValue] = useState('');
  const [result, setResult] = useState('');

  const conversions = {
    length: {
      meter: 1,
      kilometer: 1000,
      centimeter: 0.01,
      millimeter: 0.001,
      inch: 0.0254,
      foot: 0.3048,
      yard: 0.9144,
      mile: 1609.34
    },
    weight: {
      kilogram: 1,
      gram: 0.001,
      pound: 0.453592,
      ounce: 0.0283495,
      ton: 1000,
      stone: 6.35029
    },
    temperature: {
      celsius: (c: number, to: string) => {
        if (to === 'fahrenheit') return (c * 9/5) + 32;
        if (to === 'kelvin') return c + 273.15;
        return c;
      },
      fahrenheit: (f: number, to: string) => {
        if (to === 'celsius') return (f - 32) * 5/9;
        if (to === 'kelvin') return (f - 32) * 5/9 + 273.15;
        return f;
      },
      kelvin: (k: number, to: string) => {
        if (to === 'celsius') return k - 273.15;
        if (to === 'fahrenheit') return (k - 273.15) * 9/5 + 32;
        return k;
      }
    },
    area: {
      'square_meter': 1,
      'square_kilometer': 1000000,
      'square_centimeter': 0.0001,
      'square_inch': 0.00064516,
      'square_foot': 0.092903,
      'acre': 4046.86,
      'hectare': 10000
    },
    volume: {
      liter: 1,
      milliliter: 0.001,
      gallon: 3.78541,
      quart: 0.946353,
      pint: 0.473176,
      cup: 0.236588,
      'cubic_meter': 1000,
      'cubic_centimeter': 0.001
    }
  };

  const unitLabels = {
    length: {
      meter: 'm',
      kilometer: 'km',
      centimeter: 'cm',
      millimeter: 'mm',
      inch: 'in',
      foot: 'ft',
      yard: 'yd',
      mile: 'mi'
    },
    weight: {
      kilogram: 'kg',
      gram: 'g',
      pound: 'lb',
      ounce: 'oz',
      ton: 't',
      stone: 'st'
    },
    temperature: {
      celsius: '°C',
      fahrenheit: '°F',
      kelvin: 'K'
    },
    area: {
      'square_meter': 'm²',
      'square_kilometer': 'km²',
      'square_centimeter': 'cm²',
      'square_inch': 'in²',
      'square_foot': 'ft²',
      'acre': 'acre',
      'hectare': 'ha'
    },
    volume: {
      liter: 'L',
      milliliter: 'mL',
      gallon: 'gal',
      quart: 'qt',
      pint: 'pt',
      cup: 'cup',
      'cubic_meter': 'm³',
      'cubic_centimeter': 'cm³'
    }
  };

  const categories = [
    { id: 'length', name: t('categories.length') },
    { id: 'weight', name: t('categories.weight') },
    { id: 'temperature', name: t('categories.temperature') },
    { id: 'area', name: t('categories.area') },
    { id: 'volume', name: t('categories.volume') }
  ];

  const convert = () => {
    const value = parseFloat(inputValue);
    if (isNaN(value) || !fromUnit || !toUnit) return;

    let convertedValue: number;

    if (activeCategory === 'temperature') {
      const tempConversions = conversions.temperature as any;
      convertedValue = tempConversions[fromUnit](value, toUnit);
    } else {
      const categoryConversions = conversions[activeCategory as keyof typeof conversions] as any;
      const baseValue = value * categoryConversions[fromUnit];
      convertedValue = baseValue / categoryConversions[toUnit];
    }

    setResult(convertedValue.toFixed(6).replace(/\.?0+$/, ''));
  };

  const swapUnits = () => {
    const temp = fromUnit;
    setFromUnit(toUnit);
    setToUnit(temp);
    if (result) {
      setInputValue(result);
      setResult(inputValue);
    }
  };

  const clearAll = () => {
    setInputValue('');
    setResult('');
    setFromUnit('');
    setToUnit('');
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8">
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-400 mb-4">
            <RotateCcw className="w-8 h-8" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            {t('title')}
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            {t('description')}
          </p>
        </div>

        {/* Category Tabs */}
        <div className="flex flex-wrap justify-center gap-2 mb-8">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => {
                setActiveCategory(category.id);
                setFromUnit('');
                setToUnit('');
                setInputValue('');
                setResult('');
              }}
              className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                activeCategory === category.id
                  ? 'bg-orange-500 text-white'
                  : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              {category.name}
            </button>
          ))}
        </div>

        {/* Conversion Interface */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 items-center">
          {/* From Unit */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('from')}
              </label>
              <select
                value={fromUnit}
                onChange={(e) => setFromUnit(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              >
                <option value="">Select unit</option>
                {Object.keys(conversions[activeCategory as keyof typeof conversions]).map((unit) => (
                  <option key={unit} value={unit}>
                    {unitLabels[activeCategory as keyof typeof unitLabels][unit as keyof typeof unitLabels[keyof typeof unitLabels]]}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('value')}
              </label>
              <input
                type="number"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                placeholder="Enter value"
              />
            </div>
          </div>

          {/* Conversion Controls */}
          <div className="flex flex-col items-center space-y-4">
            <button
              onClick={swapUnits}
              className="p-3 bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-400 rounded-full hover:bg-orange-200 dark:hover:bg-orange-800 transition-colors duration-200"
              title="Swap units"
            >
              <RotateCcw className="w-6 h-6" />
            </button>
            <ArrowRight className="w-6 h-6 text-gray-400" />
            <button
              onClick={convert}
              className="bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-6 rounded-lg transition-colors duration-200"
            >
              {tCommon('calculate')}
            </button>
          </div>

          {/* To Unit */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('to')}
              </label>
              <select
                value={toUnit}
                onChange={(e) => setToUnit(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              >
                <option value="">Select unit</option>
                {Object.keys(conversions[activeCategory as keyof typeof conversions]).map((unit) => (
                  <option key={unit} value={unit}>
                    {unitLabels[activeCategory as keyof typeof unitLabels][unit as keyof typeof unitLabels[keyof typeof unitLabels]]}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {tCommon('result')}
              </label>
              <div className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-white font-mono text-lg">
                {result || '0'}
              </div>
            </div>
          </div>
        </div>

        <div className="mt-8 text-center">
          <button
            onClick={clearAll}
            className="bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-6 rounded-lg transition-colors duration-200"
          >
            {tCommon('clear')} All
          </button>
        </div>
      </div>
    </div>
  );
}
