'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { ArrowLeft } from 'lucide-react';

export default function ScientificCalculator() {
  const t = useTranslations('scientific');
  const tCommon = useTranslations('common');
  
  const [display, setDisplay] = useState('0');
  const [previousValue, setPreviousValue] = useState<number | null>(null);
  const [operation, setOperation] = useState<string | null>(null);
  const [waitingForOperand, setWaitingForOperand] = useState(false);
  const [memory, setMemory] = useState(0);

  const inputNumber = (num: string) => {
    if (waitingForOperand) {
      setDisplay(num);
      setWaitingForOperand(false);
    } else {
      setDisplay(display === '0' ? num : display + num);
    }
  };

  const inputDecimal = () => {
    if (waitingForOperand) {
      setDisplay('0.');
      setWaitingForOperand(false);
    } else if (display.indexOf('.') === -1) {
      setDisplay(display + '.');
    }
  };

  const clear = () => {
    setDisplay('0');
    setPreviousValue(null);
    setOperation(null);
    setWaitingForOperand(false);
  };

  const backspace = () => {
    if (display.length > 1) {
      setDisplay(display.slice(0, -1));
    } else {
      setDisplay('0');
    }
  };

  const performOperation = (nextOperation: string) => {
    const inputValue = parseFloat(display);

    if (previousValue === null) {
      setPreviousValue(inputValue);
    } else if (operation) {
      const currentValue = previousValue || 0;
      const newValue = calculate(currentValue, inputValue, operation);

      setDisplay(String(newValue));
      setPreviousValue(newValue);
    }

    setWaitingForOperand(true);
    setOperation(nextOperation);
  };

  const calculate = (firstValue: number, secondValue: number, operation: string): number => {
    switch (operation) {
      case '+':
        return firstValue + secondValue;
      case '-':
        return firstValue - secondValue;
      case '×':
        return firstValue * secondValue;
      case '÷':
        return firstValue / secondValue;
      case '^':
        return Math.pow(firstValue, secondValue);
      case '=':
        return secondValue;
      default:
        return secondValue;
    }
  };

  const performScientificOperation = (op: string) => {
    const inputValue = parseFloat(display);
    let result: number;

    switch (op) {
      case 'sin':
        result = Math.sin(inputValue * Math.PI / 180);
        break;
      case 'cos':
        result = Math.cos(inputValue * Math.PI / 180);
        break;
      case 'tan':
        result = Math.tan(inputValue * Math.PI / 180);
        break;
      case 'log':
        result = Math.log10(inputValue);
        break;
      case 'ln':
        result = Math.log(inputValue);
        break;
      case 'sqrt':
        result = Math.sqrt(inputValue);
        break;
      case 'factorial':
        result = factorial(inputValue);
        break;
      case 'pi':
        result = Math.PI;
        break;
      case 'e':
        result = Math.E;
        break;
      case '1/x':
        result = 1 / inputValue;
        break;
      case 'x²':
        result = inputValue * inputValue;
        break;
      default:
        return;
    }

    setDisplay(String(result));
    setWaitingForOperand(true);
  };

  const factorial = (n: number): number => {
    if (n < 0) return NaN;
    if (n === 0 || n === 1) return 1;
    let result = 1;
    for (let i = 2; i <= n; i++) {
      result *= i;
    }
    return result;
  };

  const handleEquals = () => {
    const inputValue = parseFloat(display);

    if (previousValue !== null && operation) {
      const newValue = calculate(previousValue, inputValue, operation);
      setDisplay(String(newValue));
      setPreviousValue(null);
      setOperation(null);
      setWaitingForOperand(true);
    }
  };

  const basicButtons = [
    { label: tCommon('clear'), action: clear, className: 'col-span-2 bg-red-500 hover:bg-red-600 text-white' },
    { label: <ArrowLeft className="w-4 h-4" />, action: backspace, className: 'bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500' },
    { label: '÷', action: () => performOperation('÷'), className: 'bg-blue-500 hover:bg-blue-600 text-white' },
    
    { label: '7', action: () => inputNumber('7'), className: 'bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600' },
    { label: '8', action: () => inputNumber('8'), className: 'bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600' },
    { label: '9', action: () => inputNumber('9'), className: 'bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600' },
    { label: '×', action: () => performOperation('×'), className: 'bg-blue-500 hover:bg-blue-600 text-white' },
    
    { label: '4', action: () => inputNumber('4'), className: 'bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600' },
    { label: '5', action: () => inputNumber('5'), className: 'bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600' },
    { label: '6', action: () => inputNumber('6'), className: 'bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600' },
    { label: '-', action: () => performOperation('-'), className: 'bg-blue-500 hover:bg-blue-600 text-white' },
    
    { label: '1', action: () => inputNumber('1'), className: 'bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600' },
    { label: '2', action: () => inputNumber('2'), className: 'bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600' },
    { label: '3', action: () => inputNumber('3'), className: 'bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600' },
    { label: '+', action: () => performOperation('+'), className: 'bg-blue-500 hover:bg-blue-600 text-white' },
    
    { label: '0', action: () => inputNumber('0'), className: 'col-span-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600' },
    { label: '.', action: inputDecimal, className: 'bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600' },
    { label: '=', action: handleEquals, className: 'bg-green-500 hover:bg-green-600 text-white' },
  ];

  const scientificButtons = [
    { label: t('functions.sin'), action: () => performScientificOperation('sin'), className: 'bg-purple-500 hover:bg-purple-600 text-white' },
    { label: t('functions.cos'), action: () => performScientificOperation('cos'), className: 'bg-purple-500 hover:bg-purple-600 text-white' },
    { label: t('functions.tan'), action: () => performScientificOperation('tan'), className: 'bg-purple-500 hover:bg-purple-600 text-white' },
    { label: t('functions.log'), action: () => performScientificOperation('log'), className: 'bg-purple-500 hover:bg-purple-600 text-white' },
    
    { label: t('functions.ln'), action: () => performScientificOperation('ln'), className: 'bg-purple-500 hover:bg-purple-600 text-white' },
    { label: t('functions.sqrt'), action: () => performScientificOperation('sqrt'), className: 'bg-purple-500 hover:bg-purple-600 text-white' },
    { label: 'x²', action: () => performScientificOperation('x²'), className: 'bg-purple-500 hover:bg-purple-600 text-white' },
    { label: 'x^y', action: () => performOperation('^'), className: 'bg-purple-500 hover:bg-purple-600 text-white' },
    
    { label: '1/x', action: () => performScientificOperation('1/x'), className: 'bg-purple-500 hover:bg-purple-600 text-white' },
    { label: t('functions.factorial'), action: () => performScientificOperation('factorial'), className: 'bg-purple-500 hover:bg-purple-600 text-white' },
    { label: t('functions.pi'), action: () => performScientificOperation('pi'), className: 'bg-purple-500 hover:bg-purple-600 text-white' },
    { label: t('functions.e'), action: () => performScientificOperation('e'), className: 'bg-purple-500 hover:bg-purple-600 text-white' },
  ];

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center">
          {t('title')}
        </h1>
        
        {/* Display */}
        <div className="bg-gray-100 dark:bg-gray-900 rounded-lg p-4 mb-6">
          <div className="text-right text-3xl font-mono text-gray-900 dark:text-white overflow-hidden">
            {display}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Scientific Functions */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Scientific Functions</h3>
            <div className="grid grid-cols-4 gap-2">
              {scientificButtons.map((button, index) => (
                <button
                  key={index}
                  onClick={button.action}
                  className={`h-12 rounded-lg font-semibold text-sm transition-colors duration-200 ${button.className}`}
                >
                  {button.label}
                </button>
              ))}
            </div>
          </div>

          {/* Basic Calculator */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Basic Operations</h3>
            <div className="grid grid-cols-4 gap-2">
              {basicButtons.map((button, index) => (
                <button
                  key={index}
                  onClick={button.action}
                  className={`h-12 rounded-lg font-semibold text-sm transition-colors duration-200 ${button.className}`}
                >
                  {button.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
