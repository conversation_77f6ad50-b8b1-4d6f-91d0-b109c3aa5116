'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { TrendingUp, DollarSign, Home, PiggyBank } from 'lucide-react';

export default function FinancialCalculator() {
  const t = useTranslations('financial');
  const tCommon = useTranslations('common');
  
  const [activeTab, setActiveTab] = useState('loan');
  
  // Loan Calculator State
  const [loanData, setLoanData] = useState({
    principal: '',
    rate: '',
    term: '',
    monthlyPayment: '',
    totalPayment: '',
    totalInterest: ''
  });

  // Investment Calculator State
  const [investmentData, setInvestmentData] = useState({
    initial: '',
    monthly: '',
    rate: '',
    years: '',
    futureValue: ''
  });

  const calculateLoan = () => {
    const principal = parseFloat(loanData.principal);
    const annualRate = parseFloat(loanData.rate) / 100;
    const monthlyRate = annualRate / 12;
    const numberOfPayments = parseFloat(loanData.term) * 12;

    if (principal && annualRate && numberOfPayments) {
      const monthlyPayment = principal * (monthlyRate * Math.pow(1 + monthlyRate, numberOfPayments)) / 
                            (Math.pow(1 + monthlyRate, numberOfPayments) - 1);
      
      const totalPayment = monthlyPayment * numberOfPayments;
      const totalInterest = totalPayment - principal;

      setLoanData(prev => ({
        ...prev,
        monthlyPayment: monthlyPayment.toFixed(2),
        totalPayment: totalPayment.toFixed(2),
        totalInterest: totalInterest.toFixed(2)
      }));
    }
  };

  const calculateInvestment = () => {
    const initial = parseFloat(investmentData.initial) || 0;
    const monthly = parseFloat(investmentData.monthly) || 0;
    const annualRate = parseFloat(investmentData.rate) / 100;
    const monthlyRate = annualRate / 12;
    const months = parseFloat(investmentData.years) * 12;

    if (annualRate && months) {
      // Future value of initial investment
      const futureInitial = initial * Math.pow(1 + monthlyRate, months);
      
      // Future value of monthly contributions
      const futureMonthly = monthly * (Math.pow(1 + monthlyRate, months) - 1) / monthlyRate;
      
      const totalFutureValue = futureInitial + futureMonthly;

      setInvestmentData(prev => ({
        ...prev,
        futureValue: totalFutureValue.toFixed(2)
      }));
    }
  };

  const clearLoan = () => {
    setLoanData({
      principal: '',
      rate: '',
      term: '',
      monthlyPayment: '',
      totalPayment: '',
      totalInterest: ''
    });
  };

  const clearInvestment = () => {
    setInvestmentData({
      initial: '',
      monthly: '',
      rate: '',
      years: '',
      futureValue: ''
    });
  };

  const tabs = [
    { id: 'loan', name: t('loan.title'), icon: DollarSign },
    { id: 'investment', name: t('investment.title'), icon: PiggyBank },
  ];

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8">
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 mb-4">
            <TrendingUp className="w-8 h-8" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            {t('title')}
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            {t('description')}
          </p>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1 mb-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex-1 flex items-center justify-center px-4 py-2 rounded-md font-medium transition-colors duration-200 ${
                  activeTab === tab.id
                    ? 'bg-white dark:bg-gray-800 text-green-600 dark:text-green-400 shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                <Icon className="w-4 h-4 mr-2" />
                {tab.name}
              </button>
            );
          })}
        </div>

        {/* Loan Calculator */}
        {activeTab === 'loan' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('loan.principal')}
                </label>
                <input
                  type="number"
                  value={loanData.principal}
                  onChange={(e) => setLoanData(prev => ({ ...prev, principal: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="100000"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('loan.rate')}
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={loanData.rate}
                  onChange={(e) => setLoanData(prev => ({ ...prev, rate: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="5.5"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('loan.term')}
                </label>
                <input
                  type="number"
                  value={loanData.term}
                  onChange={(e) => setLoanData(prev => ({ ...prev, term: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="30"
                />
              </div>
            </div>

            <div className="flex space-x-4">
              <button
                onClick={calculateLoan}
                className="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-6 rounded-lg transition-colors duration-200"
              >
                {tCommon('calculate')}
              </button>
              <button
                onClick={clearLoan}
                className="bg-gray-500 hover:bg-gray-600 text-white font-semibold py-2 px-6 rounded-lg transition-colors duration-200"
              >
                {tCommon('clear')}
              </button>
            </div>

            {/* Loan Results */}
            {loanData.monthlyPayment && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-green-50 dark:bg-green-900 rounded-lg p-4">
                  <h4 className="font-semibold text-green-800 dark:text-green-200 mb-2">
                    {t('loan.monthly_payment')}
                  </h4>
                  <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                    ${loanData.monthlyPayment}
                  </p>
                </div>
                <div className="bg-blue-50 dark:bg-blue-900 rounded-lg p-4">
                  <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">
                    {t('loan.total_payment')}
                  </h4>
                  <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    ${loanData.totalPayment}
                  </p>
                </div>
                <div className="bg-red-50 dark:bg-red-900 rounded-lg p-4">
                  <h4 className="font-semibold text-red-800 dark:text-red-200 mb-2">
                    {t('loan.total_interest')}
                  </h4>
                  <p className="text-2xl font-bold text-red-600 dark:text-red-400">
                    ${loanData.totalInterest}
                  </p>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Investment Calculator */}
        {activeTab === 'investment' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('investment.initial_amount')}
                </label>
                <input
                  type="number"
                  value={investmentData.initial}
                  onChange={(e) => setInvestmentData(prev => ({ ...prev, initial: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="10000"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('investment.monthly_contribution')}
                </label>
                <input
                  type="number"
                  value={investmentData.monthly}
                  onChange={(e) => setInvestmentData(prev => ({ ...prev, monthly: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('investment.annual_return')}
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={investmentData.rate}
                  onChange={(e) => setInvestmentData(prev => ({ ...prev, rate: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="7"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('investment.years')}
                </label>
                <input
                  type="number"
                  value={investmentData.years}
                  onChange={(e) => setInvestmentData(prev => ({ ...prev, years: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="20"
                />
              </div>
            </div>

            <div className="flex space-x-4">
              <button
                onClick={calculateInvestment}
                className="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-6 rounded-lg transition-colors duration-200"
              >
                {tCommon('calculate')}
              </button>
              <button
                onClick={clearInvestment}
                className="bg-gray-500 hover:bg-gray-600 text-white font-semibold py-2 px-6 rounded-lg transition-colors duration-200"
              >
                {tCommon('clear')}
              </button>
            </div>

            {/* Investment Results */}
            {investmentData.futureValue && (
              <div className="bg-green-50 dark:bg-green-900 rounded-lg p-6 text-center">
                <h4 className="font-semibold text-green-800 dark:text-green-200 mb-2">
                  {t('investment.future_value')}
                </h4>
                <p className="text-4xl font-bold text-green-600 dark:text-green-400">
                  ${investmentData.futureValue}
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
